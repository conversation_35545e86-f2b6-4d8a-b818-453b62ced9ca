<script lang="ts">
  import { enhance } from '$app/forms';
  import type { PageData } from './$types';
  import {
    Card,
    Button,
    FormInput,
    FormSelect,
    AlertMessage,
    PageHeader
  } from '$lib/components/ui';

  type FormDataType = {
    name?: string;
    description?: string;
    selectedStudents?: string[];
    selectedManagers?: string[];
    weekdays?: string[];
    startTimes?: string[];
    endTimes?: string[];
    locations?: string[];
  };

  type GroupFormData = {
    success: boolean;
    message: string;
    data?: FormDataType;
    groupId?: string;
  };

  let { data, form } = $props<{ data: PageData, form: GroupFormData | null }>();

  interface User {
    id: string;
    username: string;
    name?: string;
    email: string;
  }

  let name = $state('');
  let description = $state('');
  let selectedStudents = $state<string[]>([]);
  let selectedManagers = $state<string[]>([]);
  let lectureTimes = $state<{
    weekday: string;
    startTime: string;
    endTime: string;
    location: string;
  }[]>([]);

  let studentSearchTerm = $state('');
  let managerSearchTerm = $state('');
  let filteredStudents = $state<User[]>(data.students);
  let filteredLecturers = $state<User[]>(data.lecturers);

  // UI state for limiting initial display
  let showAllStudents = $state(false);
  let showAllLecturers = $state(false);

  // Constants for UI limiting
  const INITIAL_DISPLAY_LIMIT = 5;

  // Filter students based search 
  $effect(() => {
    if (!studentSearchTerm.trim()) {
      filteredStudents = data.students;
    } else {
      const term = studentSearchTerm.toLowerCase().trim();
      filteredStudents = data.students.filter((student: User) =>
        (student.name || '').toLowerCase().includes(term) ||
        student.username.toLowerCase().includes(term) ||
        student.email.toLowerCase().includes(term)
      );
    }
  });

  // Filter lecturers based on search
  $effect(() => {
    if (!managerSearchTerm.trim()) {
      filteredLecturers = data.lecturers;
    } else {
      const term = managerSearchTerm.toLowerCase().trim();
      filteredLecturers = data.lecturers.filter((lecturer: User) =>
        (lecturer.name || '').toLowerCase().includes(term) ||
        lecturer.username.toLowerCase().includes(term) ||
        lecturer.email.toLowerCase().includes(term)
      );
    }
  });

  $effect(() => {
    if (form?.data) {
      name = form.data.name || '';
      description = form.data.description || '';
      selectedStudents = form.data.selectedStudents || [];
      selectedManagers = form.data.selectedManagers || [];

      if (form.data.weekdays && form.data.weekdays.length > 0) {
        lectureTimes = form.data.weekdays.map((weekday: string, index: number) => ({
          weekday,
          startTime: form.data.startTimes?.[index] || '',
          endTime: form.data.endTimes?.[index] || '',
          location: form.data.locations?.[index] || ''
        }));
      }
    }
  });

  function addLectureTime() {
    lectureTimes = [
      ...lectureTimes,
      { weekday: 'monday', startTime: '', endTime: '', location: '' }
    ];
  }

  function removeLectureTime(index: number) {
    lectureTimes = lectureTimes.filter((_, i) => i !== index);
  }

  let isSubmitting = $state(false);

  function handleSubmit() {
    isSubmitting = true;

    return async ({ result, update }: { result: { type: string; data?: any }, update: () => Promise<void> }) => {
      isSubmitting = false;

      if (result.type === 'success' && result.data?.success) {
        if (result.data.groupId) {
          window.location.href = `/dashboard/lecturer/groups/${result.data.groupId}`;
          return;
        }
        window.location.href = '/dashboard/lecturer/groups';
        return;
      }
      await update();
    };
  }
</script>

<div>
  <PageHeader title="Create New Student Group" />

  <Card>
    <form method="POST" action="?/createGroup" use:enhance={handleSubmit} class="space-y-6">
      <div>
        <h2 class="text-lg font-medium text-gray-900 mb-4">Group Information</h2>
        <div class="space-y-4">
          <FormInput
            id="name"
            name="name"
            label="Group Name"
            value={name}
            onChange={(e: Event) => name = (e.target as HTMLInputElement).value}
            required
          />

          <FormInput
            id="description"
            name="description"
            label="Description (Optional)"
            value={description}
            onChange={(e: Event) => description = (e.target as HTMLInputElement).value}

          />
        </div>
      </div>
      <div>
        <h2 class="text-lg font-medium text-gray-900 mb-4">Add Students</h2>

        <!-- Search input for students -->
        <div class="mb-3">
          <FormInput
            id="studentSearch"
            name="studentSearch"
            placeholder="Search students by name, username or email..."
            value={studentSearchTerm}
            onChange={(e: Event) => studentSearchTerm = (e.target as HTMLInputElement).value}
          />
        </div>

        <div class="max-h-60 overflow-y-auto border border-gray-300 rounded-md p-2">
          {#if data.students.length === 0}
            <p class="text-sm text-gray-500 p-2">No students available</p>
          {:else if filteredStudents.length === 0}
            <p class="text-sm text-gray-500 p-2">No students match your search</p>
          {:else}
            {@const displayStudents = studentSearchTerm.trim() || showAllStudents ? filteredStudents : filteredStudents.slice(0, INITIAL_DISPLAY_LIMIT)}
            {#each displayStudents as student}
              <div class="flex items-center p-2 hover:bg-gray-50">
                <input
                  type="checkbox"
                  id={`student-${student.id}`}
                  name="students[]"
                  value={student.id}
                  checked={selectedStudents.includes(student.id)}
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for={`student-${student.id}`} class="ml-3 block text-sm font-medium text-gray-700">
                  {student.name || student.username} ({student.email})
                </label>
              </div>
            {/each}

            {#if !studentSearchTerm.trim() && !showAllStudents && filteredStudents.length > INITIAL_DISPLAY_LIMIT}
              <div class="p-2 border-t border-gray-200">
                <button
                  type="button"
                  onclick={() => showAllStudents = true}
                  class="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Show all {filteredStudents.length} students
                </button>
              </div>
            {/if}
          {/if}
        </div>

        {#if filteredStudents.length > 0 && studentSearchTerm.trim() !== ''}
          <p class="mt-1 text-xs text-gray-500">Showing {filteredStudents.length} of {data.students.length} students</p>
        {:else if !studentSearchTerm.trim() && filteredStudents.length > 0}
          {@const displayCount = showAllStudents ? filteredStudents.length : Math.min(INITIAL_DISPLAY_LIMIT, filteredStudents.length)}
          <p class="mt-1 text-xs text-gray-500">Showing {displayCount} of {filteredStudents.length} students</p>
        {/if}
      </div>
      <div>
        <h2 class="text-lg font-medium text-gray-900 mb-4">Add Co-managers (Optional)</h2>

        <!-- Search input for co-managers -->
        <div class="mb-3">
          <FormInput
            id="managerSearch"
            name="managerSearch"
            placeholder="Search lecturers by name, username or email..."
            value={managerSearchTerm}
            onChange={(e: Event) => managerSearchTerm = (e.target as HTMLInputElement).value}
          />
        </div>

        <div class="max-h-60 overflow-y-auto border border-gray-300 rounded-md p-2">
          {#if data.lecturers.length === 0}
            <p class="text-sm text-gray-500 p-2">No other lecturers available</p>
          {:else if filteredLecturers.length === 0}
            <p class="text-sm text-gray-500 p-2">No lecturers match your search</p>
          {:else}
            {@const displayLecturers = managerSearchTerm.trim() || showAllLecturers ? filteredLecturers : filteredLecturers.slice(0, INITIAL_DISPLAY_LIMIT)}
            {#each displayLecturers as lecturer}
              <div class="flex items-center p-2 hover:bg-gray-50">
                <input
                  type="checkbox"
                  id={`lecturer-${lecturer.id}`}
                  name="managers[]"
                  value={lecturer.id}
                  checked={selectedManagers.includes(lecturer.id)}
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for={`lecturer-${lecturer.id}`} class="ml-3 block text-sm font-medium text-gray-700">
                  {lecturer.name || lecturer.username} ({lecturer.email})
                </label>
              </div>
            {/each}

            {#if !managerSearchTerm.trim() && !showAllLecturers && filteredLecturers.length > INITIAL_DISPLAY_LIMIT}
              <div class="p-2 border-t border-gray-200">
                <button
                  type="button"
                  onclick={() => showAllLecturers = true}
                  class="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Show all {filteredLecturers.length} lecturers
                </button>
              </div>
            {/if}
          {/if}
        </div>

        {#if filteredLecturers.length > 0 && managerSearchTerm.trim() !== ''}
          <p class="mt-1 text-xs text-gray-500">Showing {filteredLecturers.length} of {data.lecturers.length} lecturers</p>
        {:else if !managerSearchTerm.trim() && filteredLecturers.length > 0}
          {@const displayCount = showAllLecturers ? filteredLecturers.length : Math.min(INITIAL_DISPLAY_LIMIT, filteredLecturers.length)}
          <p class="mt-1 text-xs text-gray-500">Showing {displayCount} of {filteredLecturers.length} lecturers</p>
        {/if}
      </div>

      <div>
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-medium text-gray-900">Lecture Schedule (Optional)</h2>
          <Button
            onClick={addLectureTime}
            variant="primary"
            size="sm"
          >
            <svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            Add Time Slot
          </Button>
        </div>

        {#if lectureTimes.length === 0}
          <p class="text-sm text-gray-500 italic">No lecture times added. Click "Add Time Slot" to schedule lectures.</p>
        {:else}
          <div class="space-y-4">
            <div class="overflow-x-auto">
              <table class="w-full border-collapse mb-4">
                <thead>
                  <tr>
                    <th class="p-2 text-left text-sm font-medium text-gray-700">Day</th>
                    <th class="p-2 text-left text-sm font-medium text-gray-700">Start Time</th>
                    <th class="p-2 text-left text-sm font-medium text-gray-700">End Time</th>
                    <th class="p-2 text-left text-sm font-medium text-gray-700">Location</th>
                    <th class="p-2 text-left text-sm font-medium text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {#each lectureTimes as lectureTime, index}
                    <tr class="border-t border-gray-200">
                      <td class="p-2">
                        <FormSelect
                          id={`weekday-${index}`}
                          name="weekday[]"
                          value={lectureTime.weekday}
                          onChange={(e: Event) => lectureTime.weekday = (e.target as HTMLSelectElement).value}
                          options={[
                            { value: 'monday', label: 'Monday' },
                            { value: 'tuesday', label: 'Tuesday' },
                            { value: 'wednesday', label: 'Wednesday' },
                            { value: 'thursday', label: 'Thursday' },
                            { value: 'friday', label: 'Friday' },
                            { value: 'saturday', label: 'Saturday' },
                            { value: 'sunday', label: 'Sunday' }
                          ]}
                        />
                      </td>

                      <td class="p-2">
                        <FormInput
                          type="time"
                          id={`startTime-${index}`}
                          name="startTime[]"
                          value={lectureTime.startTime}
                          onChange={(e: Event) => lectureTime.startTime = (e.target as HTMLInputElement).value}
                        />
                      </td>

                      <td class="p-2">
                        <FormInput
                          type="time"
                          id={`endTime-${index}`}
                          name="endTime[]"
                          value={lectureTime.endTime}
                          onChange={(e: Event) => lectureTime.endTime = (e.target as HTMLInputElement).value}
                        />
                      </td>

                      <td class="p-2">
                        <FormInput
                          type="text"
                          id={`location-${index}`}
                          name="location[]"
                          value={lectureTime.location}
                          onChange={(e: Event) => lectureTime.location = (e.target as HTMLInputElement).value}
                          placeholder="Room number, building, etc."
                        />
                      </td>

                      <td class="p-2">
                        <Button
                          onClick={() => removeLectureTime(index)}
                          variant="danger"
                          size="sm"
                          class="text-xs"
                        >
                          Remove
                        </Button>
                      </td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
          </div>
        {/if}
      </div>

      {#if form?.message && !form?.success}
        <AlertMessage
          type="error"
          message={form.message}
        />
      {/if}

      <div class="flex justify-end space-x-3">
        <Button
          variant="light"
          onClick={() => window.location.href = '/dashboard/lecturer/groups'}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Creating...' : 'Create Group'}
        </Button>
      </div>
    </form>
  </Card>
</div>
