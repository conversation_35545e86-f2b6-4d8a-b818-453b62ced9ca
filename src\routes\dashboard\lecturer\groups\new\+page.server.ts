import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'lecturer') {
    return redirect(303, '/auth/login');
  }

  if (!user.isApproved) {
    return redirect(303, '/pending');
  }

  const students = await db
    .select({
      id: table.user.id,
      username: table.user.username,
      name: table.user.name,
      email: table.user.email
    })
    .from(table.user)
    .where(eq(table.user.role, 'student'));

  const lecturers = await db
    .select({
      id: table.user.id,
      username: table.user.username,
      name: table.user.name,
      email: table.user.email
    })
    .from(table.user)
    .where(
      eq(table.user.role, 'lecturer')
    );

  return {
    user,
    students,
    lecturers: lecturers.filter(l => l.id !== user.id) // exclude current
  };
};

export const actions: Actions = {
  createGroup: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const name = formData.get('name')?.toString().trim();
    const description = formData.get('description')?.toString().trim() || null;
    const selectedStudents = formData.getAll('students[]').map(s => s.toString());
    const selectedManagers = formData.getAll('managers[]').map(m => m.toString());

    const weekdays = formData.getAll('weekday[]').map(w => w.toString());
    const startTimes = formData.getAll('startTime[]').map(t => t.toString());
    const endTimes = formData.getAll('endTime[]').map(t => t.toString());
    const locations = formData.getAll('location[]').map(l => l.toString());

    if (!name) {
      return fail(400, {
        success: false,
        message: 'Group name is required',
        data: {
          name,
          description,
          selectedStudents,
          selectedManagers,
          weekdays,
          startTimes,
          endTimes,
          locations
        }
      });
    }

    try {
      const groupId = crypto.randomUUID();

      await db.insert(table.group).values({
        id: groupId,
        name,
        description,
        createdBy: user.id,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      if (selectedStudents.length > 0) {
        await Promise.all(
          selectedStudents.map(async (studentId) => {
            await db.insert(table.groupMember).values({
              id: crypto.randomUUID(),
              groupId,
              studentId,
              addedBy: user.id,
              addedAt: new Date()
            });
          })
        );
      }

      if (selectedManagers.length > 0) {
        await Promise.all(
          selectedManagers.map(async (lecturerId) => {
            await db.insert(table.groupManager).values({
              id: crypto.randomUUID(),
              groupId,
              lecturerId,
              addedBy: user.id,
              addedAt: new Date()
            });
          })
        );
      }

      const lectureTimes = weekdays.map((weekday, index) => ({
        weekday,
        startTime: startTimes[index],
        endTime: endTimes[index],
        location: locations[index] || null
      })).filter(lt => lt.weekday && lt.startTime && lt.endTime);

      if (lectureTimes.length > 0) {
        await Promise.all(
          lectureTimes.map(async (lt) => {
            await db.insert(table.lectureTime).values({
              id: crypto.randomUUID(),
              groupId,
              weekday: lt.weekday as any, // hacky fixas once again
              startTime: lt.startTime,
              endTime: lt.endTime,
              location: lt.location,
              createdBy: user.id,
              createdAt: new Date(),
              updatedAt: new Date()
            });
          })
        );
      }

      return {
        success: true,
        message: 'Group created successfully',
        groupId
      };
    } catch (error) {
      console.error('Error creating group:', error);
      return fail(500, {
        success: false,
        message: 'An error occurred while creating the group',
        data: {
          name,
          description,
          selectedStudents,
          selectedManagers,
          weekdays,
          startTimes,
          endTimes,
          locations
        }
      });
    }
  }
};
